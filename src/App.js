import React from 'react';
import { HashRouter as Router, Switch, Route } from 'react-router-dom';
import ProjectManagement from './components/ProjectManagement/ProjectManagement.js';
import ProjectDemo from './components/ProjectManagement/ProjectDemo.js';
import ProjectFormTest from './components/ProjectManagement/ProjectFormTest.js';
import CurrencyTest from './components/ProjectManagement/CurrencyTest.js';
import WeeklyBudgetMultiFormTest from './components/ProjectManagement/WeeklyBudgetMultiFormTest.js';
import TitleGenerationTest from './components/ProjectManagement/TitleGenerationTest.js';

import BrandManagementPage from './components/BrandManagement/BrandManagementPage.js';
import SupplierManagement from './components/SupplierManagement/SupplierManagement.js';
import ReportsPage from './components/Reports/ReportsPage.js';
import SystemManagement from './components/SystemManagement/SystemManagement.js';
import ModernLayout from './components/Layout/ModernLayout.js';
import ModernDashboard from './components/Dashboard/ModernDashboard.js';
import LocaleTest from './components/Test/LocaleTest.js';
import StoreInitializationTest from './components/Examples/StoreInitializationTest.js';
import SyncInitializationTest from './components/Examples/SyncInitializationTest.js';
import QuickStoreTest from './components/Examples/QuickStoreTest.js';
import AsyncPreloadTest from './components/Examples/AsyncPreloadTest.js';
import ApiDataTest from './components/Examples/ApiDataTest.js';
import PermissionDataTest from './components/Examples/PermissionDataTest.js';
import PermissionControlDemo from './components/Examples/PermissionControlDemo.js';
import ProjectManagementWithPermission from './components/Examples/ProjectManagementWithPermission.js';
import * as dd from 'dingtalk-jsapi';
import { antdLocale } from './config/locale.js';
import AppInitializer from './components/App/AppInitializer.js';

import './styles/global.css';
import { ConfigProvider } from 'antd';

// const { host } = config;


// const alert = function (msg) {
//   dd.device.notification.alert({
//     message: msg,
//     title: '提示', // 可传空
//     buttonName: '确定',
//     onSuccess() {
//     },
//     onFail() {},
//   }).catch(() => {
//     // alert(msg);
//   });
// };


class H5AppQS extends React.Component {
  componentDidMount() {
    // 设置导航栏标题
    dd.biz.navigation.setTitle({
      title: '项目财务管理', // 控制标题文本，空字符串表示显示默认文本
      onSuccess() {},
      onFail() {},
    }).catch((err) => { console.log(`${err }`); });
  }

  render() {
    const { userInfo, onLogout, currentUser } = this.props;
    console.log('[ currentUser ] >', currentUser);
    console.log('[ onLogout ] >', onLogout);
    return (
      <ModernLayout
        userInfo={userInfo}
        onLogout={onLogout}
        currentUser={currentUser}
      >
        <Switch>
          <Route exact path="/">
            <ModernDashboard />
          </Route>
          <Route path="/projects">
            <ProjectManagement />
          </Route>
          <Route path="/project-form-test">
            <ProjectFormTest />
          </Route>
          <Route path="/currency-test">
            <CurrencyTest />
          </Route>
          <Route path="/multi-budget-test">
            <WeeklyBudgetMultiFormTest />
          </Route>
          <Route path="/title-test">
            <TitleGenerationTest />
          </Route>

          <Route path="/brands">
            <BrandManagementPage />
          </Route>
          <Route path="/demo">
            <ProjectDemo />
          </Route>
          <Route path="/suppliers">
            <SupplierManagement />
          </Route>
          <Route path="/system">
            <SystemManagement />
          </Route>
          <Route path="/reports">
            <ReportsPage />
          </Route>
          {/* 登录路由已移至 AppInitializer 中处理 */}
          <Route path="/locale-test">
            <LocaleTest />
          </Route>
          <Route path="/store-test">
            <StoreInitializationTest />
          </Route>
          <Route path="/sync-test">
            <SyncInitializationTest />
          </Route>
          <Route path="/quick-test">
            <QuickStoreTest />
          </Route>
          <Route path="/async-test">
            <AsyncPreloadTest />
          </Route>
          <Route path="/api-test">
            <ApiDataTest />
          </Route>
          <Route path="/permission-test">
            <PermissionDataTest />
          </Route>
          <Route path="/permission-demo">
            <PermissionControlDemo />
          </Route>
          <Route path="/project-permission">
            <ProjectManagementWithPermission />
          </Route>
        </Switch>
      </ModernLayout>
    );
  }
}

function App(props) {
  return (
    <ConfigProvider locale={antdLocale}>
      <AppInitializer >
        <Router>
          <div className="App">
            <H5AppQS onLogout={props.onLogout} config={props.config} />
          </div>
        </Router>
      </AppInitializer>
    </ConfigProvider>
  );
}

export default App;
