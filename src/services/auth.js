// 用户认证服务
import config from '../config.js';
import * as dd from 'dingtalk-jsapi';

const { host } = config;
const API_BASE_URL = `${host}/api`;

// 开发模式配置 - 可以跳过鉴权用于测试
// const DEV_MODE = {
//   // 是否跳过鉴权（设置为true时直接使用模拟用户登录）
//   SKIP_AUTH: process.env.REACT_APP_SKIP_AUTH === 'true' || true, // 默认开启跳过鉴权

//   // 模拟用户信息（跳过鉴权时使用）
//   MOCK_USER: {
//     userid: 'dev_user_001',
//     name: '开发测试用户',
//     mobile: '13800138000',
//     department: '技术部',
//     position: '系统管理员',
//     deptIds: ['1', '2'],
//     isBoss: false,
//     isAdmin: true,
//     roles: ['admin'],
//     permissions: [
//       'project:view',
//       'project:create',
//       'project:edit',
//       'project:delete',
//       'brand:view',
//       'brand:create',
//       'brand:edit',
//       'brand:delete',
//       'stats:view',
//       'file:upload',
//     ],
//   },
// };

// Token存储键名
const ACCESS_TOKEN_KEY = 'accessToken';
const REFRESH_TOKEN_KEY = 'refreshToken';
const USER_INFO_KEY = 'userInfo';

// 认证服务类
class AuthService {
  constructor() {
    this.accessToken = this.getStoredAccessToken();
    this.refreshToken = this.getStoredRefreshToken();
    this.userInfo = this.getStoredUserInfo();
  }

  // 获取存储的accessToken
  getStoredAccessToken() {
    try {
      return localStorage.getItem(ACCESS_TOKEN_KEY);
    } catch (error) {
      console.error('Failed to get access token from localStorage:', error);
      return null;
    }
  }

  // 获取存储的refreshToken
  getStoredRefreshToken() {
    try {
      return localStorage.getItem(REFRESH_TOKEN_KEY);
    } catch (error) {
      console.error('Failed to get refresh token from localStorage:', error);
      return null;
    }
  }

  // 获取存储的用户信息
  getStoredUserInfo() {
    try {
      const userInfo = localStorage.getItem(USER_INFO_KEY);
      return userInfo ? JSON.parse(userInfo) : null;
    } catch (error) {
      console.error('Failed to get user info from localStorage:', error);
      return null;
    }
  }

  // 存储accessToken
  setAccessToken(accessToken) {
    try {
      this.accessToken = accessToken;
      localStorage.setItem(ACCESS_TOKEN_KEY, accessToken);
    } catch (error) {
      console.error('Failed to store access token:', error);
    }
  }

  // 存储refreshToken
  setRefreshToken(refreshToken) {
    try {
      this.refreshToken = refreshToken;
      localStorage.setItem(REFRESH_TOKEN_KEY, refreshToken);
    } catch (error) {
      console.error('Failed to store refresh token:', error);
    }
  }

  // 存储token（兼容旧方法）
  setToken(token) {
    this.setAccessToken(token);
  }

  // 存储用户信息
  setUserInfo(userInfo) {
    try {
      this.userInfo = userInfo;
      localStorage.setItem(USER_INFO_KEY, JSON.stringify(userInfo));
    } catch (error) {
      console.error('Failed to store user info:', error);
    }
  }

  // 清除认证信息
  clearAuth() {
    try {
      this.accessToken = null;
      this.refreshToken = null;
      this.userInfo = null;
      localStorage.removeItem(ACCESS_TOKEN_KEY);
      localStorage.removeItem(REFRESH_TOKEN_KEY);
      localStorage.removeItem(USER_INFO_KEY);
    } catch (error) {
      console.error('Failed to clear auth info:', error);
    }
  }

  // 检查是否已登录
  isAuthenticated() {
    return !!this.accessToken;
  }

  // 获取当前accessToken
  getAccessToken() {
    return this.accessToken;
  }

  // 获取当前refreshToken
  getRefreshToken() {
    return this.refreshToken;
  }

  // 获取当前token（兼容旧方法）
  getToken() {
    return this.accessToken;
  }

  // 获取当前用户信息
  getUserInfo() {
    return this.userInfo;
  }

  // 检查现有认证状态
  async checkExistingAuth() {
    const savedAccessToken = this.getStoredAccessToken();
    const savedRefreshToken = this.getStoredRefreshToken();
    const savedUserInfo = this.getStoredUserInfo();

    if (savedAccessToken && savedUserInfo) {
      this.accessToken = savedAccessToken;
      this.refreshToken = savedRefreshToken;
      this.userInfo = savedUserInfo;

      console.log('发现已保存的认证信息，验证中...');

      // 验证token是否仍然有效
      try {
        const response = await fetch(`${API_BASE_URL}/auth/me`, {
          headers: {
            Authorization: `Bearer ${this.accessToken}`,
          },
        });

        if (response.ok) {
          const data = await response.json();
          if (data.success) {
            console.log('已保存的认证信息有效', data);
            return { success: true, data: { user: this.userInfo } };
          }
        }
      } catch (error) {
        console.log('验证已保存认证信息失败:', error);
      }

      // 如果验证失败，清除保存的信息
      this.clearAuth();
    }

    // 开始钉钉免登认证
    return this.initDingTalkAuth();
  }

  // 初始化钉钉免登认证
  async initDingTalkAuth() {
    // 检查是否在钉钉环境中
    if (typeof dd === 'undefined') {
      throw new Error('请在钉钉客户端中打开此页面');
    }

    // 钉钉环境检查
    return new Promise((resolve, reject) => {
      dd.ready(() => {
        console.log('钉钉环境准备就绪');

        // 获取免登码
        dd.runtime.permission.requestAuthCode({
          // 生产环境和开发环境
          corpId: process.env.NODE_ENV === 'production' ? 'ding660cccf3aa1024874ac5d6980864d335' : 'dinge21dd1a7d6663db3a39a90f97fcb1e09', // 使用您的企业corpId
          onSuccess: (result) => {
            console.log('获取免登码成功:', result);
            this.authenticateWithServer(result.code)
              .then(resolve)
              .catch(reject);
          },
          onFail: (err) => {
            console.error('获取免登码失败:', err);
            reject(new Error(`获取免登码失败: ${err.message}`));
          },
        });
      });

      dd.error((err) => {
        console.error('钉钉初始化失败:', err);
        reject(new Error(`钉钉初始化失败: ${err.message}`));
      });
    });
  }

  // 向服务器验证免登码
  async authenticateWithServer(code) {
    try {
      console.log('向服务器发送免登码:', code);

      // 调用后端API验证免登码并获取JWT token
      const response = await fetch(`${API_BASE_URL}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          authCode: code,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || '认证请求失败');
      }

      if (data.success) {
        // 保存JWT token
        this.accessToken = data.data.accessToken;
        this.refreshToken = data.data.refreshToken;
        this.userInfo = data.data.user;

        // 保存到localStorage
        this.setAccessToken(this.accessToken);
        this.setRefreshToken(this.refreshToken);
        this.setUserInfo(this.userInfo);

        console.log('accessToken', this.accessToken);
        console.log('登录成功，用户信息:', this.userInfo);
        console.log('访问令牌已保存:', `${this.accessToken.substring(0, 20) }...`);

        return data;
      } else {
        throw new Error(data.message || '登录失败');
      }
    } catch (error) {
      console.error('服务器认证失败:', error);
      throw new Error(`服务器认证失败: ${ error.message}`);
    }
  }

  // 钉钉免登（主入口）
  async dingtalkLogin() {
    try {
      return await this.checkExistingAuth();
    } catch (error) {
      console.error('DingTalk login failed:', error);
      throw error;
    }
  }

  // 传统登录（保留作为备用）
  async login(credentials) {
    try {
      // 使用真实API
      const response = await fetch(`${API_BASE_URL}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(credentials),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || `HTTP error! status: ${response.status}`);
      }

      if (data.success && data.data.accessToken) {
        this.setAccessToken(data.data.accessToken);
        this.setRefreshToken(data.data.refreshToken);
        this.setUserInfo(data.data.user);
        return data;
      } else {
        throw new Error(data.message || '登录失败');
      }
    } catch (error) {
      console.error('Login failed:', error);
      throw error;
    }
  }

  // 登出
  async logout() {
    try {
      // 如果有token，调用后端登出接口
      if (this.accessToken) {
        // 使用真实API
        // await fetch(`${API_BASE_URL}/auth/logout`, {
        //   method: 'POST',
        //   headers: {
        //     Authorization: `Bearer ${this.accessToken}`,
        //     'Content-Type': 'application/json',
        //   },
        // });
        localStorage.removeItem(ACCESS_TOKEN_KEY);
        localStorage.removeItem(REFRESH_TOKEN_KEY);
        localStorage.removeItem(USER_INFO_KEY);
        window.location.reload();
      }
    } catch (error) {
      console.error('Logout API call failed:', error);
      // 即使API调用失败，也要清除本地认证信息
    } finally {
      this.clearAuth();
    }
  }

  // 刷新token（兼容旧方法）
  async refreshToken() {
    const success = await this.refreshAccessToken();
    if (success) {
      return { success: true, data: { token: this.accessToken } };
    } else {
      this.clearAuth();
      throw new Error('Token刷新失败');
    }
  }

  // 刷新访问令牌
  async refreshAccessToken() {
    try {
      if (!this.refreshToken) {
        console.error('缺少刷新令牌');
        return false;
      }

      // 使用真实API
      const response = await fetch(`${API_BASE_URL}/auth/refresh`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          refreshToken: this.refreshToken,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || `HTTP error! status: ${response.status}`);
      }

      if (data.success && data.data.accessToken) {
        this.accessToken = data.data.accessToken;
        this.setAccessToken(this.accessToken);
        console.log('访问令牌刷新成功');
        return true;
      } else {
        console.error('刷新令牌失败:', data.message);
        return false;
      }
    } catch (error) {
      console.error('刷新令牌异常:', error);
      return false;
    }
  }

  // 获取用户信息
  async fetchUserInfo() {
    try {
      if (!this.accessToken) {
        throw new Error('No token available');
      }

      // 使用真实API
      const response = await fetch(`${API_BASE_URL}/auth/me`, {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${this.accessToken}`,
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || `HTTP error! status: ${response.status}`);
      }

      if (data.success && data.data) {
        this.setUserInfo(data.data);
        return data;
      } else {
        throw new Error(data.message || '获取用户信息失败');
      }
    } catch (error) {
      console.error('Fetch user info failed:', error);
      throw error;
    }
  }

  // 检查权限
  hasPermission(permission) {
    if (!this.userInfo || !this.userInfo.permissions) {
      return false;
    }
    return this.userInfo.permissions.includes(permission);
  }

  // 检查角色
  hasRole(role) {
    if (!this.userInfo || !this.userInfo.roles) {
      return false;
    }
    return this.userInfo.roles.includes(role);
  }
}

// 创建认证服务实例
const authService = new AuthService();

// 权限常量
export const PERMISSIONS = {
  // 项目权限
  PROJECT_VIEW: 'project:project:view',
  PROJECT_CREATE: 'project:project:create',
  PROJECT_EDIT: 'project:project:edit',
  PROJECT_DELETE: 'project:project:delete',
  PROJECT_MANAGE: 'project:project:manage',

  // 品牌权限
  BRAND_VIEW: 'brand:brand:view',
  BRAND_CREATE: 'brand:brand:create',
  BRAND_EDIT: 'brand:brand:edit',
  BRAND_DELETE: 'brand:brand:delete',
  BRAND_MANAGE: 'brand:brand:manage',

  // 供应商权限
  SUPPLIER_VIEW: 'supplier:supplier:view',
  SUPPLIER_CREATE: 'supplier:supplier:create',
  SUPPLIER_EDIT: 'supplier:supplier:edit',
  SUPPLIER_DELETE: 'supplier:supplier:delete',
  SUPPLIER_MANAGE: 'supplier:supplier:manage',

  // 用户权限
  USER_VIEW: 'user:user:view',
  USER_CREATE: 'user:user:create',
  USER_EDIT: 'user:user:edit',
  USER_DELETE: 'user:user:delete',
  USER_MANAGE: 'user:user:manage',

  // 角色权限
  ROLE_VIEW: 'role:role:view',
  ROLE_CREATE: 'role:role:create',
  ROLE_EDIT: 'role:role:edit',
  ROLE_DELETE: 'role:role:delete',
  ROLE_MANAGE: 'role:role:manage',

  // 权限管理权限
  PERMISSION_VIEW: 'permission:permission:view',
  PERMISSION_CREATE: 'permission:permission:create',
  PERMISSION_EDIT: 'permission:permission:edit',
  PERMISSION_DELETE: 'permission:permission:delete',
  PERMISSION_MANAGE: 'permission:permission:manage',

  // 收入权限
  REVENUE_VIEW: 'revenue:revenue:view',
  REVENUE_CREATE: 'revenue:revenue:create',
  REVENUE_EDIT: 'revenue:revenue:edit',
  REVENUE_DELETE: 'revenue:revenue:delete',
  REVENUE_CONFIRM: 'revenue:revenue:confirm',

  // 预算权限
  BUDGET_VIEW: 'budget:budget:view',
  BUDGET_CREATE: 'budget:budget:create',
  BUDGET_EDIT: 'budget:budget:edit',
  BUDGET_DELETE: 'budget:budget:delete',
  BUDGET_APPROVE: 'budget:budget:approve',

  // 报表权限
  REPORT_VIEW: 'report:report:view',
  REPORT_EXPORT: 'report:report:export',

  // 统计权限
  STATS_VIEW: 'stats:stats:view',

  // 文件权限
  FILE_UPLOAD: 'file:file:upload',
  FILE_DELETE: 'file:file:delete',

  // 系统权限
  SYSTEM_MANAGE: 'system:system:manage',
  SYSTEM_CONFIG: 'system:system:config',
};

// 角色常量
export const ROLES = {
  ADMIN: 'admin',
  PROJECT_MANAGER: 'project_manager',
  FINANCE_MANAGER: 'finance_manager',
  CONTENT_MANAGER: 'content_manager',
  BRAND_MANAGER: 'brand_manager',
  SUPPLIER_MANAGER: 'supplier_manager',
  OPERATOR: 'operator',
  VIEWER: 'viewer',
};

export default authService;
