import React, { useState, useEffect } from 'react';
import {
  Modal,
  Transfer,
  message,
  Card,
  Avatar,
  // Tag,
  Descriptions,
} from 'antd';
import { roleApi, userApi } from '../../services/api';

const RoleAssignModal = ({ visible, user, onCancel, onSuccess }) => {
  console.log('[ visible, user, onCancel, onSuccess ] >', visible, user, onCancel, onSuccess);
  const [loading, setLoading] = useState(false);
  const [targetKeys, setTargetKeys] = useState([]);
  const [selectedKeys, setSelectedKeys] = useState([]);
  const [roles, setRoles] = useState([]);

  // 初始化用户角色
  useEffect(() => {
    if (!user) return;

    // 同时请求
    Promise.all([
      roleApi.getRoles({ pageSize: 1000 }),
      roleApi.getRolesByUserId(user.userid),
    ])
      .then(([rolesResponse, userRolesResponse]) => {
        if (rolesResponse.success) {
          setRoles(rolesResponse.data.roles || []);
        }
        if (userRolesResponse.success) {
          // setTargetKeys(userRolesResponse.data.directRoles || []);
          const select = userRolesResponse.data.directRoles.map((item) => item.id);
          setTargetKeys(select || []);
        }
        console.log('[ rolesResponse, userRolesResponse ] >', selectedKeys, targetKeys);
      })
      .catch((error) => {
        console.error('Role assign failed:', error);
        message.error('角色分配失败');
      });
  }, [user]);

  // 处理角色变更
  const handleChange = (newTargetKeys) => {
    setTargetKeys(newTargetKeys);
  };

  // 处理选择变更
  const handleSelectChange = (sourceSelectedKeys, targetSelectedKeys) => {
    setSelectedKeys([...sourceSelectedKeys, ...targetSelectedKeys]);
  };

  // 提交角色分配
  const handleSubmit = async () => {
    setLoading(true);

    try {
      const response = await userApi.updateUserRoles(user.userid, targetKeys);

      if (response.success) {
        message.success('角色分配成功');
        onSuccess();
      } else {
        message.error(response.message || '角色分配失败');
      }
    } catch (error) {
      console.error('Role assign failed:', error);
      message.error('角色分配失败');
    } finally {
      setLoading(false);
    }
  };

  // 渲染角色项
  const renderRoleItem = (item) => {
    // const customLabel = (
    //   <div style={{ display: 'flex', alignItems: 'center', padding: '8px 0' }}>
    //     <div style={{ flex: 1 }}>
    //       <div style={{ fontWeight: 'bold', marginBottom: 4 }}>
    //         {item.name}
    //       </div>
    //       <div style={{ fontSize: '12px', color: '#666' }}>
    //         {item.description || '暂无描述'}
    //       </div>
    //       <div style={{ marginTop: 4 }}>
    //         {item.permissions && item.permissions.length > 0 && (
    //           <Tag color="blue" size="small">
    //             {item.permissions.length} 个权限
    //           </Tag>
    //         )}
    //         {item.status === 'active' ? (
    //           <Tag color="green" size="small">正常</Tag>
    //         ) : (
    //           <Tag color="red" size="small">禁用</Tag>
    //         )}
    //       </div>
    //     </div>
    //   </div>
    // );
    return {
      key: item.id,
      title: item.displayName,
      description: item.description,
      disabled: !item.isActive,
    };
  };

  if (!user) {
    return null;
  }
  const dataSource = roles.map(renderRoleItem);

  return (
    <Modal
      title="分配角色"
      visible={visible}
      onOk={handleSubmit}
      onCancel={onCancel}
      confirmLoading={loading}
      width={800}
      destroyOnClose
    >
      <div style={{ marginBottom: 16 }}>
        <Card size="small">
          <Descriptions column={2} size="small">
            <Descriptions.Item label="用户">
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <Avatar
                  size="small"
                  src={user.avatar}
                  icon="user"
                  style={{ marginRight: 8 }}
                />
                {user.name}
              </div>
            </Descriptions.Item>
            <Descriptions.Item label="手机号">
              {user.mobile || '-'}
            </Descriptions.Item>
            <Descriptions.Item label="部门">
              {user.departmentName || '-'}
            </Descriptions.Item>
            {/* <Descriptions.Item label="当前角色">
              {user.roles && user.roles.length > 0 ? (
                user.roles.map((role) => (
                  <Tag key={role.id} color="blue" style={{ marginBottom: 4 }}>
                    {role.name}
                  </Tag>
                ))
              ) : (
                <Tag color="default">无角色</Tag>
              )}
            </Descriptions.Item> */}
          </Descriptions>
        </Card>
      </div>

      <Transfer
        dataSource={dataSource}
        titles={['可选角色', '已分配角色']}
        targetKeys={targetKeys}
        selectedKeys={selectedKeys}
        onChange={handleChange}
        onSelectChange={handleSelectChange}
        render={(item) => item.title}
        listStyle={{
          width: 300,
          height: 400,
        }}
        operations={['分配', '移除']}
        showSearch
        searchPlaceholder="搜索角色"
        filterOption={(inputValue, option) =>
          option.title.props.children[0].props.children[0].props.children
            .toLowerCase()
            .includes(inputValue.toLowerCase())
          }
      />

      <div style={{ marginTop: 16, fontSize: '12px', color: '#666' }}>
        <p>• 左侧显示所有可用角色，右侧显示用户当前拥有的角色</p>
        <p>• 禁用状态的角色无法分配给用户</p>
        <p>• 角色权限会立即生效，请谨慎操作</p>
      </div>
    </Modal>
  );
};

export default RoleAssignModal;
