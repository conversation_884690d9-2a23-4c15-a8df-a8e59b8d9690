import React, { Component } from 'react';
import {
  Row,
  Col,
  Card,
  Statistic,
  Icon,
  // Progress,
  // List,
  // Avatar,
  Button,
  // Tag,
  // Timeline,
  // Divider,
} from 'antd';
import './ModernDashboard.css';

class ModernDashboard extends Component {
  constructor(props) {
    super(props);
    this.state = {
      // loading: false,
      dashboardData: {
        stats: {
          totalProjects: 24,
          activeProjects: 8,
          totalRevenue: 1250000,
          monthlyRevenue: 180000,
          totalSuppliers: 45,
          activeSuppliers: 32,
          pendingPayments: 85000,
          completionRate: 85,
        },
        recentProjects: [
          {
            id: 1,
            name: '春季品牌推广项目',
            status: 'active',
            progress: 75,
            budget: 500000,
            manager: '张三',
            deadline: '2024-04-15',
          },
          {
            id: 2,
            name: '夏季新品发布',
            status: 'planning',
            progress: 25,
            budget: 300000,
            manager: '李四',
            deadline: '2024-05-20',
          },
          {
            id: 3,
            name: '年中促销活动',
            status: 'completed',
            progress: 100,
            budget: 200000,
            manager: '王五',
            deadline: '2024-03-30',
          },
        ],
        recentActivities: [
          {
            id: 1,
            type: 'project',
            title: '新建项目：春季品牌推广',
            time: '2小时前',
            user: '张三',
          },
          {
            id: 2,
            type: 'payment',
            title: '供应商付款：达人服务费',
            time: '4小时前',
            user: '财务部',
          },
          {
            id: 3,
            type: 'approval',
            title: '预算审批：夏季新品发布',
            time: '1天前',
            user: '李四',
          },
          {
            id: 4,
            type: 'completion',
            title: '项目完成：年中促销活动',
            time: '2天前',
            user: '王五',
          },
        ],
      },
    };
  }

  getStatusColor = (status) => {
    const colors = {
      active: '#52c41a',
      planning: '#1890ff',
      completed: '#722ed1',
      paused: '#faad14',
      cancelled: '#ff4d4f',
    };
    return colors[status] || '#d9d9d9';
  };

  getStatusText = (status) => {
    const texts = {
      active: '进行中',
      planning: '规划中',
      completed: '已完成',
      paused: '已暂停',
      cancelled: '已取消',
    };
    return texts[status] || '未知';
  };

  getActivityIcon = (type) => {
    const icons = {
      project: 'project',
      payment: 'dollar',
      approval: 'check-circle',
      completion: 'trophy',
    };
    return icons[type] || 'info-circle';
  };

  render() {
    const { dashboardData } = this.state;
    const { stats } = dashboardData;

    return (
      <div className="modern-dashboard">
        {/* 欢迎区域 */}
        <div className="welcome-section">
          <div className="welcome-content">
            <h1 className="welcome-title">欢迎回来！</h1>
            <p className="welcome-subtitle">
              今天是 {new Date().toLocaleDateString('zh-CN', {
              year: 'numeric',
              month: 'long',
              day: 'numeric',
              weekday: 'long',
            })}
            </p>
          </div>
          <div className="welcome-actions">
            <Button type="primary" size="large" icon="plus">
              新建项目
            </Button>
          </div>
        </div>

        {/* 统计卡片 */}
        <Row gutter={[24, 24]} className="stats-row">
          <Col xs={24} sm={12} lg={8}>
            <Card className="stat-card stat-card-primary">
              <Statistic
                title="项目总数"
                value={stats.totalProjects}
                prefix={<Icon type="project" />}
                suffix="个"
              />
              <div className="stat-trend">
                <Icon type="arrow-up" className="trend-up" />
                <span>较上月 +12%</span>
              </div>
            </Card>
          </Col>

          <Col xs={24} sm={12} lg={8}>
            <Card className="stat-card stat-card-success">
              <Statistic
                title="当月总收入"
                value={stats.totalRevenue}
                prefix={<Icon type="dollar" />}
                precision={0}
                formatter={(value) => `¥${value.toLocaleString()}`}
              />
              <div className="stat-trend">
                <Icon type="arrow-up" className="trend-up" />
                <span>较上月 +8.5%</span>
              </div>
            </Card>
          </Col>

          <Col xs={24} sm={12} lg={8}>
            <Card className="stat-card stat-card-warning">
              <Statistic
                title="今年总收入"
                value={stats.totalRevenue}
                prefix={<Icon type="dollar" />}
                precision={0}
                formatter={(value) => `¥${value.toLocaleString()}`}
              />
              <div className="stat-trend">
                <Icon type="arrow-up" className="trend-up" />
                <span>较上月 +8.5%</span>
              </div>
            </Card>
          </Col>
        </Row>

      </div>
    );
  }
}

export default ModernDashboard;
