import React, { useState, useEffect, useCallback } from 'react';
import { Spin, Card, Progress, Typography } from 'antd';
import { LoadingOutlined } from '@ant-design/icons';
import { StoreProvider } from '../../store';
import authService from '../../services/auth';
import DingTalkAuth from '../Auth/DingTalkAuth';
import LoginForm from '../Auth/LoginForm';
import './AppInitializer.css';

const { Title, Text } = Typography;

/**
 * 应用初始化器组件
 * 负责认证检查和应用启动时的数据初始化
 */
const AppInitializer = ({ children }) => {
  const [initStage, setInitStage] = useState('auth'); // auth, loading, completed
  const [progress, setProgress] = useState(0);
  const [stageText, setStageText] = useState('正在检查认证状态...');
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [userInfo, setUserInfo] = useState(null);
  const [showFallbackLogin, setShowFallbackLogin] = useState(false);
  console.log('[ isAuthenticated ] >', isAuthenticated);
  // 更新初始化阶段
  const updateStage = useCallback((stage) => {
    // 初始化阶段配置
    const stages = {
      auth: { progress: 20, text: '正在检查认证状态...' },
      loading: { progress: 60, text: '正在并行加载数据...' },
      completed: { progress: 100, text: '初始化完成' },
    };

    setInitStage(stage);
    setProgress(stages[stage].progress);
    setStageText(stages[stage].text);
  }, []);

  // 处理登录成功
  const handleLoginSuccess = useCallback((userData) => {
    console.log('🎉 登录成功，开始加载数据');
    setIsAuthenticated(true);
    setUserInfo(userData.user);
    updateStage('loading');
  }, [updateStage]);

  // 处理登出
  const handleLogout = useCallback(() => {
    console.log('👋 用户登出');
    authService.clearAuth();
    setIsAuthenticated(false);
    setUserInfo(null);
    setShowFallbackLogin(false);
    updateStage('auth');
  }, [updateStage]);

  // 处理备用登录
  const handleFallbackLogin = useCallback(() => {
    setShowFallbackLogin(true);
  }, []);

  // Store 初始化完成回调
  const handleStoreInitialized = useCallback(() => {
    console.log('🎉 所有数据加载完成，准备渲染应用');
    setTimeout(() => {
      updateStage('completed');
    }, 800); // 短暂延迟，让用户看到完成状态
  }, [updateStage]);

  // 认证错误回调
  const handleAuthError = useCallback(() => {
    console.log('🔐 认证失败，返回登录页面');
    authService.clearAuth();
    setIsAuthenticated(false);
    setUserInfo(null);
    setShowFallbackLogin(false);
    updateStage('auth');
  }, [updateStage]);

  // 检查认证状态
  useEffect(() => {
    const checkAuth = async () => {
      try {
        const authenticated = authService.isAuthenticated();
        const user = authService.getUserInfo();

        if (authenticated && user) {
          console.log('✅ 发现已保存的认证信息');
          setIsAuthenticated(true);
          setUserInfo(user);
          updateStage('loading');
        } else {
          console.log('❌ 未找到有效认证信息');
          updateStage('auth');
        }
      } catch (error) {
        console.error('❌ 认证检查失败:', error);
        updateStage('auth');
      }
    };

    checkAuth();
  }, [updateStage]);

  // 如果未认证，显示登录界面
  if (initStage === 'auth') {
    if (showFallbackLogin) {
      // 显示传统登录表单
      return (
        <LoginForm
          onLoginSuccess={handleLoginSuccess}
        />
      );
    } else {
      // 显示钉钉免登
      return (
        <DingTalkAuth
          onLoginSuccess={handleLoginSuccess}
          onFallbackLogin={handleFallbackLogin}
        />
      );
    }
  }

  // 已认证，进行数据初始化
  return (
    <StoreProvider
      onInitialized={handleStoreInitialized}
      onAuthError={handleAuthError}
    >
      <AppContent
        initStage={initStage}
        progress={progress}
        stageText={stageText}
        userInfo={userInfo}
        onLogout={handleLogout}
      >
        {children}
      </AppContent>
    </StoreProvider>
  );
};

/**
 * 应用内容组件
 * 处理数据加载完成后的应用渲染
 */
const AppContent = ({ children, initStage, progress, stageText, userInfo, onLogout }) => {
  // 如果初始化未完成，显示加载界面
  if (initStage !== 'completed') {
    return <AppLoadingScreen progress={progress} stageText={stageText} />;
  }

  // 初始化完成，渲染主应用，并传递用户信息和登出回调
  return React.cloneElement(children, { userInfo, onLogout });
};

/**
 * 应用加载屏幕组件
 */
const AppLoadingScreen = ({ progress, stageText }) => {
  const antIcon = <LoadingOutlined style={{ fontSize: 48, color: '#1890ff' }} spin />;

  return (
    <div className="app-loading-container">
      <div className="app-loading-content">
        <Card className="loading-card" bordered={false}>
          <div className="loading-header">
            <div className="loading-icon">
              <Spin indicator={antIcon} />
            </div>
            <Title level={3} className="loading-title">
              财务管理系统
            </Title>
            <Text className="loading-subtitle">Project Finance Management</Text>
          </div>

          <div className="loading-progress">
            <Progress
              percent={progress}
              strokeColor={{
                '0%': '#108ee9',
                '100%': '#87d068',
              }}
              trailColor="#f0f0f0"
              strokeWidth={8}
              showInfo={false}
            />
            <Text className="progress-text">{stageText}</Text>
          </div>

          <div className="loading-tips">
            <Text type="secondary">
              正在并行加载数据，为您节省宝贵时间...
            </Text>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default AppInitializer;
